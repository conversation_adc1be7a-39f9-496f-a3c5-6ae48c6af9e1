/**
 * AG Grid-based Unified Preview Grid Handler
 * Provides spreadsheet-like behavior with synchronized column resizing and row detail view
 * PERFORMANCE OPTIMIZED: Includes proper cleanup and virtualization
 */

// Debounce utility function
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const context = this;
        const later = () => {
            timeout = null;
            func.apply(context, args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Global variable for the unified preview AG Grid API
let unifiedPreviewGridApi = null;
let unifiedPreviewGridColumnApi = null;

// Performance optimization settings
const AG_GRID_PERFORMANCE = {
    ROW_BUFFER: 10,           // Increased for better display
    MAX_ROWS_IN_MEMORY: 200,  // Increased to show more rows
    CACHE_BLOCK_SIZE: 50,     // Larger cache blocks for better performance
    MAX_BLOCKS_IN_CACHE: 4,   // Keep more blocks (200 rows total)
    ANIMATION_ENABLED: false, // Disable animations for performance
    VIRTUALIZATION_ENABLED: true
};

/**
 * Destroy existing AG Grid instance properly to prevent memory leaks
 */
function destroyUnifiedPreviewGrid() {
    if (unifiedPreviewGridApi) {
        console.log('🧹 Destroying existing AG Grid instance');
        try {
            // Clear all data first
            unifiedPreviewGridApi.setRowData([]);
            
            // Remove event listeners
            unifiedPreviewGridApi.removeEventListener('bodyScrollStart');
            unifiedPreviewGridApi.removeEventListener('bodyScrollEnd');
            unifiedPreviewGridApi.removeEventListener('gridReady');
            unifiedPreviewGridApi.removeEventListener('firstDataRendered');
            unifiedPreviewGridApi.removeEventListener('cellDoubleClicked');
            
            // Destroy the grid
            unifiedPreviewGridApi.destroy();
        } catch (error) {
            console.warn('Warning during AG Grid cleanup:', error);
        }
        
        unifiedPreviewGridApi = null;
        unifiedPreviewGridColumnApi = null;
        
        // Clear the container
        const gridContainer = document.getElementById('unifiedPreviewAGGrid');
        if (gridContainer) {
            gridContainer.innerHTML = '';
        }
    }
}

/**
 * Helper to detect if a target column currently has pending transformation requests.
 * Relies on the global `pendingRequests` array maintained in import_wizard_mapping_logic.js
 */
function isColumnComputing(targetColumn) {
    try {
        return Array.isArray(pendingRequests) && pendingRequests.some(req => req.targetColumn === targetColumn);
    } catch (e) {
        // Fallback: treat as not computing if pendingRequests is not accessible
        return false;
    }
}

/**
 * Row info button cell renderer for the first column
 */
class RowInfoCellRenderer {
    init(params) {
        const rowIndex = params.rowIndex;
        const displayRowNumber = rowIndex + 1;
        
        this.eGui = document.createElement('div');
        this.eGui.style.display = 'flex';
        this.eGui.style.flexDirection = 'column';
        this.eGui.style.alignItems = 'center';
        this.eGui.style.justifyContent = 'center';
        this.eGui.style.padding = '2px';
        this.eGui.style.height = '100%';
        this.eGui.style.gap = '2px';
        
        this.eGui.innerHTML = `
            <span style="font-weight: bold; color: #6c757d; font-size: 0.75rem;">${displayRowNumber}</span>
            <div style="display: flex; gap: 2px;">
                <button type="button" class="btn btn-sm btn-outline-info" 
                        style="padding: 1px 4px; font-size: 0.65rem; border-radius: 2px;"
                        title="View source data for row ${displayRowNumber}">
                    <i class="fas fa-info-circle"></i>
                </button>
                <button type="button" class="clear-row-btn" 
                        title="Clear all cells in row ${displayRowNumber}">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </div>
        `;
        
        // Add click handler for the info button
        const infoButton = this.eGui.querySelector('.btn-outline-info');
        this.infoButtonHandler = (e) => {
            e.stopPropagation();
            showRowSourceDataModal(rowIndex);
        };
        infoButton.addEventListener('click', this.infoButtonHandler);
        
        // Add click handler for the clear button
        const clearButton = this.eGui.querySelector('.clear-row-btn');
        this.clearButtonHandler = (e) => {
            e.stopPropagation();
            clearRowData(rowIndex);
        };
        clearButton.addEventListener('click', this.clearButtonHandler);
    }
    
    getGui() {
        return this.eGui;
    }
    
    // PERFORMANCE: Implement cleanup
    destroy() {
        if (this.eGui) {
            // Remove event listeners to prevent memory leaks
            const infoButton = this.eGui.querySelector('.btn-outline-info');
            const clearButton = this.eGui.querySelector('.clear-row-btn');
            
            // Store function references if they were added during init
            if (infoButton && this.infoButtonHandler) {
                infoButton.removeEventListener('click', this.infoButtonHandler);
            }
            if (clearButton && this.clearButtonHandler) {
                clearButton.removeEventListener('click', this.clearButtonHandler);
            }
        }
    }
}

function isCellEditable(column, rowIndex) {
    // Check if the cell is editable (logic type or has error)
    // Ensure columnLogic and columnResults are defined before accessing them
    if (typeof columnLogic !== 'undefined' && columnLogic && columnLogic[column]) {
        return true;
    }
    
    if (typeof columnResults !== 'undefined' && columnResults &&
        columnResults[column] &&
        typeof columnResults[column][rowIndex] !== 'undefined') {
        const value = columnResults[column][rowIndex];
        if (typeof value === 'string' && value.startsWith('Error:')) {
            return true;
        }
    }
    
    return false;
}

/**
 * Initialize the unified preview AG Grid with virtual scrolling for performance
 */
function initUnifiedPreviewAGGrid() {
    // Destroy existing grid first to prevent memory leaks
    destroyUnifiedPreviewGrid();
    
    const gridContainer = document.getElementById('unifiedPreviewAGGrid');
    if (!gridContainer) {
        console.error('AG Grid container not found');
        return;
    }

    console.log('🚀 Initializing optimized AG Grid with performance settings');

    const gridOptions = {
        // PERFORMANCE OPTIMIZATIONS
        defaultColDef: {
            resizable: false,
            sortable: false,
            filter: false,
            suppressMenu: true,
            minWidth: 60,
            maxWidth: 250,
            flex: 0,
            width: 120
        },
        
        // Row model and virtualization settings
        rowModelType: 'clientSide',
        suppressRowVirtualisation: false, // Enable row virtualization
        rowBuffer: AG_GRID_PERFORMANCE.ROW_BUFFER,
        
        // Animation and interaction settings
        animateRows: AG_GRID_PERFORMANCE.ANIMATION_ENABLED,
        suppressColumnMoveAnimation: true,
        suppressRowClickSelection: true,
        suppressCellFocus: true,
        suppressRowHoverHighlight: true,
        suppressCellSelection: false,
        
        // Layout settings
        headerHeight: 0,
        rowHeight: 35,
        suppressHorizontalScroll: false,
        enableRangeSelection: false,
        enableRangeHandle: false,
        
        // Pagination for large datasets - disable for small datasets
        pagination: false, // Will be enabled dynamically for large datasets
        paginationPageSize: AG_GRID_PERFORMANCE.MAX_ROWS_IN_MEMORY,
        paginationAutoPageSize: false,
        
        // Memory management
        maxConcurrentDatasourceRequests: 1, // Reduced concurrent requests
        cacheBlockSize: AG_GRID_PERFORMANCE.CACHE_BLOCK_SIZE,
        maxBlocksInCache: AG_GRID_PERFORMANCE.MAX_BLOCKS_IN_CACHE,
        
        columnDefs: [
            {
                headerName: '',
                field: 'rowNumber',
                cellRenderer: RowInfoCellRenderer,
                width: 80,
                minWidth: 80,
                maxWidth: 80,
                resizable: false,
                suppressSizeToFit: true,
                pinned: 'left',
                lockPosition: true,
                suppressMovable: true,
                cellClass: 'ag-pinned-row-number',
                headerClass: 'ag-pinned-header'
            }
        ],
        rowData: [],
        
        // Event handlers
        onBodyScrollStart: () => {
            $('#mappingHeadersWrapper').addClass('scrolling');
        },
        onBodyScrollEnd: () => {
            setTimeout(() => {
                $('#mappingHeadersWrapper').removeClass('scrolling');
            }, 100);
        },
        onGridReady: (params) => {
            unifiedPreviewGridApi = params.api;
            unifiedPreviewGridColumnApi = params.columnApi;
            console.log('✅ Optimized AG Grid initialized with virtualization');
            
            // Force pinned columns to stay pinned
            params.api.setPinnedLeftWidth(80);
            
            // Setup scroll synchronization and force alignment
            setTimeout(() => {
                setupScrollSynchronization();
            }, 100);
        },
        onFirstDataRendered: (params) => {
            const rowCount = params.api.getDisplayedRowCount();
            console.log(`📊 AG Grid rendered ${rowCount} rows`);

            // Enable pagination only for large datasets
            if (originalData && originalData.length > AG_GRID_PERFORMANCE.MAX_ROWS_IN_MEMORY) {
                console.log(`📈 Large dataset (${originalData.length} rows), enabling pagination`);
                params.api.paginationSetPageSize(AG_GRID_PERFORMANCE.MAX_ROWS_IN_MEMORY);
                params.api.setPagination(true);
            } else {
                console.log(`📊 Small dataset (${originalData ? originalData.length : 0} rows), pagination disabled`);
                params.api.setPagination(false);
            }
        },
        onCellDoubleClicked: (params) => {
            const column = params.colDef.field;
            const rowIndex = params.rowIndex;
            const currentValue = params.value;
            
            if (column && column !== 'rowNumber' && rowIndex >= 0 && isCellEditable(column, rowIndex)) {
                showCellEditModal(column, rowIndex, currentValue);
            }
        },
        
        // Performance optimization callbacks
        onModelUpdated: () => {
            // Clear any cached references to improve memory usage
            if (window.gc && typeof window.gc === 'function') {
                // Force garbage collection if available (Chrome with --js-flags="--expose-gc")
                setTimeout(() => window.gc(), 1000);
            }
        }
    };

    // Create the grid with performance monitoring
    console.time('AG Grid Creation');
    new agGrid.Grid(gridContainer, gridOptions);
    console.timeEnd('AG Grid Creation');
}

/**
 * Update the unified preview grid based on current mappings
 */
function updateUnifiedPreviewGrid(targetColumn = null, newRowData = null) {
    // Enhanced safety checks
    if (!unifiedPreviewGridApi) {
        console.warn('Unified preview grid API not initialized yet');
        return;
    }

    // Check if originalData is available
    if (!originalData || originalData.length === 0) {
        console.warn('No original data available for preview grid update');
        return;
    }

    // Check if mappingDefinitions is available
    if (!mappingDefinitions || mappingDefinitions.length === 0) {
        console.warn('No mapping definitions available for preview grid update');
        return;
    }

    const unifiedPreviewGrid = $('#unifiedPreviewGrid');

    if (!unifiedPreviewGrid.length) {
        console.error("Unified preview grid elements not found");
        return;
    }
    
    // Get all mapped columns (one-to-one, string values, and logic-based)
    const mappedColumnsForPreview = [];
    
    // Helper function to check if a column is deactivated
    function isColumnDeactivated(targetCol) {
        // Check deactivated columns tracking
        if (window.deactivatedColumns && window.deactivatedColumns[targetCol]) {
            return true;
        }
        
        // Check mapping type from the dropdown if column element exists
        const columnElement = $(`#horizontal-mapping-container .mapping-column-cell[data-target-column="${targetCol}"]`);
        if (columnElement.length) {
            const mapTypeSelect = columnElement.find('.map-type-select');
            if (mapTypeSelect.length && mapTypeSelect.val() === 'deactivated') {
                return true;
            }
        }
        
        return false;
    }
    
    // PERFORMANCE: For small datasets, show all data. For large datasets, use pagination
    const isLargeDataset = originalData && originalData.length > 1000; // Increased threshold
    const dataToProcess = originalData; // Always process all data, let pagination handle display

    // Add one-to-one mappings
    for (const targetCol in columnMappings) {
        if (!isColumnDeactivated(targetCol)) {
            mappedColumnsForPreview.push({
                targetColumn: targetCol,
                sourceColumn: columnMappings[targetCol],
                type: 'one-to-one'
            });
            
            // PERFORMANCE: Only process visible data
            if (!columnResults[targetCol] || columnResults[targetCol].length !== dataToProcess.length) {
                columnResults[targetCol] = dataToProcess.map(row => 
                    row.hasOwnProperty(columnMappings[targetCol]) ? row[columnMappings[targetCol]] : null
                );
            }
        }
    }
    
    // Add string value mappings
    for (const targetCol in columnStringValues) {
        if (!isColumnDeactivated(targetCol)) {
            mappedColumnsForPreview.push({
                targetColumn: targetCol,
                value: columnStringValues[targetCol],
                type: 'string'
            });
            
            // PERFORMANCE: Use fill() for better performance
            if (!columnResults[targetCol] || columnResults[targetCol].length !== dataToProcess.length) {
                const stringValue = columnStringValues[targetCol];
                columnResults[targetCol] = new Array(dataToProcess.length).fill(stringValue);
            }
        }
    }
    
    // Add logic mappings
    for (const targetCol in columnLogic) {
        if (!isColumnDeactivated(targetCol)) {
            mappedColumnsForPreview.push({
                targetColumn: targetCol,
                logic: columnLogic[targetCol],
                type: 'logic'
            });
        }
    }
    
    // Sort columns according to ui_column_order
    mappedColumnsForPreview.sort((a, b) => {
        const orderA = window.uiColumnOrder ? window.uiColumnOrder.indexOf(a.targetColumn) : -1;
        const orderB = window.uiColumnOrder ? window.uiColumnOrder.indexOf(b.targetColumn) : -1;
        
        if (orderA !== -1 && orderB !== -1) {
            return orderA - orderB;
        }
        if (orderA !== -1) return -1;
        if (orderB !== -1) return 1;
        return 0;
    });
    
    // If no mappings are configured, show the original data columns
    if (mappedColumnsForPreview.length === 0) {
        console.log('No column mappings configured, showing original data columns');

        // Get source columns from the first row of original data
        if (originalData && originalData.length > 0) {
            const sourceColumns = Object.keys(originalData[0]);
            sourceColumns.forEach(sourceCol => {
                mappedColumnsForPreview.push({
                    targetColumn: sourceCol,
                    sourceColumn: sourceCol,
                    type: 'source-preview'
                });
            });
            console.log(`Added ${sourceColumns.length} source columns for preview`);
        }

        // If still no columns, hide the grid
        if (mappedColumnsForPreview.length === 0) {
            unifiedPreviewGrid.addClass('d-none');
            return;
        }
    }
    
    // Show the grid
    unifiedPreviewGrid.removeClass('d-none');
    console.log(`Grid made visible with ${mappedColumnsForPreview.length} columns`);
    
    // Build column definitions for AG Grid
    const columnDefs = [
        {
            headerName: '#',
            field: 'rowNumber',
            cellRenderer: RowInfoCellRenderer,
            width: 80,
            minWidth: 80,
            maxWidth: 80,
            resizable: false,
            suppressSizeToFit: true,
            pinned: 'left',
            lockPosition: true,
            suppressMovable: true,
            cellClass: 'ag-pinned-row-number',
            headerClass: 'ag-pinned-header'
        }
    ];
    
    // Add columns for each mapped target column
    mappedColumnsForPreview.forEach((col, index) => {
        // Check if this column should be visible based on optional column toggle
        const columnElement = $(`#horizontal-mapping-container .mapping-column-cell[data-target-column="${col.targetColumn}"]`);
        const isColumnVisible = columnElement.length === 0 || columnElement.is(':visible');
        
        // Also check if the column is deactivated
        const isDeactivated = window.deactivatedColumns && window.deactivatedColumns[col.targetColumn];
        
        // Check mapping type from the dropdown if column element exists
        let mapTypeDeactivated = false;
        if (columnElement.length) {
            const mapTypeSelect = columnElement.find('.map-type-select');
            mapTypeDeactivated = mapTypeSelect.length && mapTypeSelect.val() === 'deactivated';
        }
        
        if (!isColumnVisible || isDeactivated || mapTypeDeactivated) {
            return; // Skip this column if it's hidden or deactivated
        }
        
        // Calculate width based on column name length with better sizing
        const columnNameLength = col.targetColumn.length;
        const autoWidth = Math.min(Math.max(columnNameLength * 8 + 40, 80), 200); // Min 80px, max 200px
        
        columnDefs.push({
            headerName: col.targetColumn,
            field: col.targetColumn,
            width: autoWidth,
            minWidth: 60,
            maxWidth: 250,
            resizable: false, // Ensured columns added here are also not resizable
            cellRenderer: (params) => {
                return createMappedCellRenderer(params, col);
            },
            cellClass: (params) => { // MODIFIED
                const value = params.value;
                const classes = [];
                if (value && String(value).startsWith('Error:')) {
                    classes.push('text-danger');
                }
                // Add 'cell-editable' class if the cell is indeed editable,
                // to provide a visual cue for double-clicking.
                if (isCellEditable(params.colDef.field, params.rowIndex)) {
                    classes.push('cell-editable');
                }
                return classes.length > 0 ? classes : null; // Return null if no classes for AG Grid
            },
        });
    });
    
    // Update the grid with new column definitions
    unifiedPreviewGridApi.setColumnDefs(columnDefs);
    
    // Build row data - show all data for small datasets, use pagination for large ones
    const isLargeDataset = originalData.length > AG_GRID_PERFORMANCE.MAX_ROWS_IN_MEMORY;
    const rowCount = isLargeDataset ?
        Math.min(previewRowLimit || AG_GRID_PERFORMANCE.MAX_ROWS_IN_MEMORY, originalData.length) :
        originalData.length;
    const rowData = [];

    console.log(`Building row data: ${rowCount} rows from ${originalData.length} total rows`);
    
    for (let i = 0; i < rowCount; i++) {
        const row = {
            rowNumber: i + 1,
            rowIndex: i // Store the actual row index for reference
        };
        
        mappedColumnsForPreview.forEach(col => {
            let cellValue = '';
            
            if (col.type === 'source-preview' && originalData[i].hasOwnProperty(col.sourceColumn)) {
                // Show original source data when no mappings are configured
                const sourceValue = originalData[i][col.sourceColumn];
                cellValue = sourceValue !== undefined && sourceValue !== null ? sourceValue : '';
            } else if (columnResults[col.targetColumn] && columnResults[col.targetColumn][i] !== undefined) {
                const value = columnResults[col.targetColumn][i];
                cellValue = value !== undefined && value !== null ? value : '';
            } else if (col.type === 'one-to-one') {
                const sourceValue = originalData[i][col.sourceColumn];
                cellValue = sourceValue !== undefined && sourceValue !== null ? sourceValue : '';
            } else if (col.type === 'string') {
                cellValue = col.value;
            } else if (col.type === 'logic') {
                // Show the spinner only if there are pending requests for this column
                if (typeof isColumnComputing === 'function' ? isColumnComputing(col.targetColumn) : (typeof pendingRequests !== 'undefined' && pendingRequests.some(r => r.targetColumn === col.targetColumn))) {
                    cellValue = 'Computing...';
                } else {
                    cellValue = '';
                }
            }
            
            row[col.targetColumn] = cellValue;
        });
        
        rowData.push(row);
    }
    
    // Set the row data
    console.log(`Setting row data: ${rowData.length} rows with columns:`, mappedColumnsForPreview.map(c => c.targetColumn));
    unifiedPreviewGridApi.setRowData(rowData);

    // Debug: Log first few rows
    if (rowData.length > 0) {
        console.log('First row data sample:', rowData[0]);
    }
    
    // Auto-save if there's a job and mappings
    if (currentJobId && mappedColumnsForPreview.length > 0) {
        if (window.saveColumnResultsTimeout) {
            clearTimeout(window.saveColumnResultsTimeout);
        }
        window.saveColumnResultsTimeout = setTimeout(() => {
            saveCurrentJobProgress(true); 
        }, 2000); 
    }

    // Ensure alignment and header structure are updated after data/column changes
    // debouncedForceColumnAlignment(); // REMOVED
    setTimeout(updateUnifiedPreviewGridHeaderStructure, 350); 

    // console.log('Unified preview grid updated for target column:', targetColumn);

    if (newRowData && newRowData.length > 0) {
        unifiedPreviewGridApi.applyTransaction({ update: newRowData });
    } else {
        // The rowData was already set above in the main loop, no need for fallback
        // This prevents double data loading which was causing display issues
        console.log(`Grid updated with ${rowData.length} rows`);
    }
    
    // Sync column widths from mapping headers to AG Grid after update - REMOVED syncAllColumnWidthsFromMappingHeaders call
    // syncAllColumnWidthsFromMappingHeaders(); 

    // Clear any cell selection after grid update to prevent stale selections - REMOVED clearCellSelection call
    // clearCellSelection();
}

/**
 * Create cell renderer for mapped columns
 */
function createMappedCellRenderer(params, columnConfig) {
    const value = params.value;
    
    if (value === undefined || value === null || value === '') {
        return '<em class="text-muted">empty</em>';
    }
    
    const valueStr = String(value);
    
    // Check if it's an error message
    if (valueStr.startsWith('Error:')) {
        return `<span class="text-danger">${escapeHtml(valueStr)}</span>`;
    }
    
    // Check if it's computing
    if (valueStr === 'Computing...') {
        return '<span class="text-muted"><i class="fas fa-cog fa-spin me-1"></i>Computing...</span>';
    }
    
    return escapeHtml(valueStr);
}

/**
 * Sync column widths from AG Grid back to mapping headers
 */
function syncColumnWidthsToMappingHeaders(event) {
    if (!event.columns || event.columns.length === 0) return;
    
    event.columns.forEach(column => {
        const field = column.getColDef().field;
        if (field && field !== 'rowNumber') { // Skip the row number column
            const newWidth = Math.round(column.getActualWidth()); // Round to avoid sub-pixel issues
            const mappingColumn = $(`#horizontal-mapping-container .mapping-column-cell[data-target-column="${field}"]`);
            if (mappingColumn.length && newWidth > 0) {
                mappingColumn.css({
                    'width': newWidth + 'px',
                    'max-width': newWidth + 'px',
                    'min-width': newWidth + 'px',
                    'flex': 'none'
                });
            }
        }
    });
}

/**
 * Sync column widths from mapping headers to AG Grid
 */
function syncAllColumnWidthsFromMappingHeaders() {
    if (!unifiedPreviewGridApi) return;
    
    $('#horizontal-mapping-container .mapping-column-cell').each(function() {
        const targetColumn = $(this).data('target-column');
        if (targetColumn) {
            const currentWidth = $(this).outerWidth();
            const column = unifiedPreviewGridApi.getColumn(targetColumn);
            if (column && currentWidth > 0) {
                unifiedPreviewGridApi.setColumnWidth(column, currentWidth);
            }
        }
    });
}

/**
 * Sync mapping headers widths to match AG Grid columns
 */
function syncMappingHeadersWidthsToAGGrid() {
    if (!unifiedPreviewGridApi) return;
    
    // Get all columns from AG Grid
    const allColumns = unifiedPreviewGridApi.getAllGridColumns();
    
    allColumns.forEach(column => {
        const field = column.getColDef().field;
        if (field && field !== 'rowNumber') {
            const columnWidth = column.getActualWidth();
            const mappingColumn = $(`#horizontal-mapping-container .mapping-column-cell[data-target-column="${field}"]`);
            if (mappingColumn.length && columnWidth > 0) {
                mappingColumn.css({
                    'width': columnWidth + 'px',
                    'max-width': columnWidth + 'px',
                    'min-width': columnWidth + 'px',
                    'flex': 'none'
                });
            }
        }
    });
}

/**
 * Force perfect alignment between mapping headers and AG Grid columns
 */
function forceColumnAlignment() {
    if (!unifiedPreviewGridApi) return;
    
    // console.log('Forcing column alignment...');
    
    // Step 1: Get the current order of mapping headers
    const headerOrder = [];
    $('#horizontal-mapping-container .mapping-column-cell').each(function() {
        const columnName = $(this).attr('data-target-column');
        if (columnName) {
            headerOrder.push(columnName);
        }
    });
    
    // console.log('Header order:', headerOrder);
    
    // Step 2: Get AG Grid columns and reorder them to match header order
    const allColumns = unifiedPreviewGridApi.getAllGridColumns();
    const dataColumns = allColumns.filter(col => col.getColDef().field !== 'rowNumber');
    
    // Step 3: Reorder AG Grid columns to match header order
    const reorderedColumns = [];
    headerOrder.forEach(fieldName => {
        const column = dataColumns.find(col => col.getColDef().field === fieldName);
        if (column) {
            reorderedColumns.push(column);
        }
    });
    
    // Add the row number column at the beginning
    const rowNumberColumn = allColumns.find(col => col.getColDef().field === 'rowNumber');
    if (rowNumberColumn) {
        reorderedColumns.unshift(rowNumberColumn);
    }
    
    // Apply the new column order
    if (reorderedColumns.length > 0) {
        // unifiedPreviewGridApi.setColumnOrder(reorderedColumns.map(col => col.getColDef().field));
        const columnOrderState = reorderedColumns.map(col => ({ colId: col.getColDef().field }));
        unifiedPreviewGridApi.applyColumnState({ state: columnOrderState, applyOrder: true });
    }
    
    // Step 4: Sync widths from headers to AG Grid
    setTimeout(() => {
        $('#horizontal-mapping-container .mapping-column-cell').each(function() {
            const targetColumn = $(this).data('target-column');
            if (targetColumn) {
                const headerWidth = Math.round($(this).outerWidth()); // Round to avoid sub-pixel issues
                const column = unifiedPreviewGridApi.getColumn(targetColumn);
                if (column && headerWidth > 0) {
                    unifiedPreviewGridApi.setColumnWidth(column, headerWidth);
                }
            }
        });
        
        // console.log('Column alignment completed');
        
        // Debug: Log the final state
        debugColumnAlignment();
    }, 50);
}

// Create a debounced version of forceColumnAlignment
const debouncedForceColumnAlignment = debounce(forceColumnAlignment, 300);

/**
 * Debug function to help troubleshoot column alignment issues
 */
function debugColumnAlignment() {
    if (!unifiedPreviewGridApi) return;
    
    // console.log('=== Column Alignment Debug ===');
    
    // Log header information
    const headers = [];
    $('#horizontal-mapping-container .mapping-column-cell').each(function() {
        const columnName = $(this).attr('data-target-column');
        const width = $(this).outerWidth();
        headers.push({ name: columnName, width: width });
    });
    // console.log('Mapping Headers:', headers);
    
    // Log AG Grid column information
    const gridColumns = [];
    const allColumns = unifiedPreviewGridApi.getAllGridColumns();
    allColumns.forEach(column => {
        const field = column.getColDef().field;
        const width = column.getActualWidth();
        gridColumns.push({ field: field, width: width });
    });
    // console.log('AG Grid Columns:', gridColumns);
    
    // console.log('=== End Debug ===');
}

/**
 * Show the row source data modal
 */
function showRowSourceDataModal(rowIndex) {
    if (!originalData || rowIndex >= originalData.length) {
        console.error('Invalid row index or no data available');
        return;
    }
    
    const rowData = originalData[rowIndex];
    const displayRowNumber = rowIndex + 1;
    
    // Update modal title
    $('#modalRowNumber').text(displayRowNumber);
    
    // Clear existing table data
    const tableBody = $('#rowSourceDataTableBody');
    tableBody.empty();
    
    // Populate table with source data
    Object.keys(rowData).forEach(columnName => {
        const value = rowData[columnName];
        const displayValue = value !== undefined && value !== null ? 
            escapeHtml(String(value)) : '<em class="text-muted">empty</em>';
        
        const row = $(`
            <tr>
                <td class="fw-medium">${escapeHtml(columnName)}</td>
                <td style="word-break: break-word;">${displayValue}</td>
            </tr>
        `);
        
        tableBody.append(row);
    });
    
    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('rowSourceDataModal'));
    modal.show();
}

/**
 * Setup scroll synchronization between mapping headers and AG Grid
 */
function setupScrollSynchronization() {
    if (!unifiedPreviewGridApi) return;
    
    const mappingScrollable = $('#mappingHeadersScrollable');
    const agGridElement = $('#unifiedPreviewAGGrid');
    
    if (!mappingScrollable.length || !agGridElement.length) return;
    
    let isSyncingToGrid = false;
    let isSyncingToHeaders = false;
    
    // Remove any existing scroll listeners to prevent duplicates
    mappingScrollable.off('scroll.agGridSync');
    agGridElement.off('scroll.agGridSync');
    
    // Listen for mapping headers scroll and sync to AG Grid
    mappingScrollable.on('scroll.agGridSync', function() {
        if (isSyncingToHeaders) return;
        
        isSyncingToGrid = true;
        const scrollLeft = $(this).scrollLeft();
        
        // Sync with AG Grid using the API
        try {
            const gridApi = unifiedPreviewGridApi;
            if (gridApi && gridApi.ensureColumnVisible) {
                const scrollContainer = agGridElement.find('.ag-body-horizontal-scroll')[0];
                if (scrollContainer) {
                    scrollContainer.scrollLeft = scrollLeft;
                } else {
                    const bodyViewport = agGridElement.find('.ag-body-viewport')[0];
                    if (bodyViewport) {
                        bodyViewport.scrollLeft = scrollLeft;
                    }
                }
            }
        } catch (error) {
            console.warn('Error syncing scroll to AG Grid:', error);
        }
        
        setTimeout(() => { isSyncingToGrid = false; }, 50);
    });
    
    // Listen for AG Grid scroll and sync to mapping headers
    const syncFromGrid = function(event) {
        if (isSyncingToGrid) return;
        
        isSyncingToHeaders = true;
        const scrollLeft = event.target.scrollLeft || 0;
        
        try {
            mappingScrollable.scrollLeft(scrollLeft);
        } catch (error) {
            console.warn('Error syncing scroll from AG Grid:', error);
        }
        
        setTimeout(() => { isSyncingToHeaders = false; }, 50);
    };
    
    // Attach scroll listeners to AG Grid scroll containers
    const scrollSelectors = [
        '.ag-body-horizontal-scroll',
        '.ag-body-viewport',
        '.ag-center-cols-viewport'
    ];
    
    scrollSelectors.forEach(selector => {
        const elements = agGridElement.find(selector);
        if (elements.length) {
            elements.on('scroll.agGridSync', syncFromGrid);
        }
    });
    
    // Also listen for horizontal scroll events on the entire AG Grid
    agGridElement.on('scroll.agGridSync', function(event) {
        if (event.target === this) {
            syncFromGrid(event);
        }
    });
}

/**
 * Initialize when document is ready
 */
$(document).ready(function() {
    console.log('🚀 Initializing unified preview AG Grid on document ready');

    // Initialize the grid
    initUnifiedPreviewAGGrid();

    // Set up scroll synchronization after a delay
    setTimeout(() => {
        setupScrollSynchronization();
    }, 500);

    // Mark that the grid system is ready
    window.unifiedPreviewGridReady = true;
    console.log('✅ Unified preview grid system marked as ready');
    
    $('head').append(`
        <style>
            .cell-editable {
                cursor: pointer;
            }
            .cell-editable:hover {
                background-color: #f0f8ff !important;
                box-shadow: inset 0 0 2px #0d6efd !important;
            }
            #unifiedPreviewAGGrid .ag-header-cell {
                font-size: 0.85rem;
            }
            #unifiedPreviewAGGrid .ag-cell {
                font-size: 0.85rem;
            }
            #unifiedPreviewAGGrid .ag-pinned-left-cols-container {
                background: #f8f9fa !important;
                border-right: 2px solid #dee2e6 !important;
                box-shadow: 2px 0 4px rgba(0,0,0,0.1);
                z-index: 15 !important;
                position: relative !important;
            }
            #unifiedPreviewAGGrid .ag-pinned-left-header {
                background: #f8f9fa !important;
                border-right: 2px solid #dee2e6 !important;
                z-index: 15 !important;
            }
            #unifiedPreviewAGGrid .ag-cell.ag-pinned-row-number {
                background: #f8f9fa !important;
                font-weight: bold;
                color: #6c757d;
                border-right: 2px solid #dee2e6 !important;
            }
            #unifiedPreviewAGGrid .ag-header-cell.ag-pinned-header {
                background: #f8f9fa !important;
                border-right: 2px solid #dee2e6 !important;
                font-weight: bold;
                color: #6c757d;
            }
            #unifiedPreviewAGGrid .ag-body-horizontal-scroll {
                scrollbar-width: thin;
                scrollbar-color: #6c757d #f1f1f1;
            }
            #mappingHeadersWrapper.scrolling {
                pointer-events: none;
            }
            #unifiedPreviewAGGrid .ag-pinned-left-cols-container {
                position: sticky !important;
                left: 0 !important;
                z-index: 15 !important;
            }
        </style>
    `);
});

/**
 * Legacy compatibility - update grid header structure
 */
function updateUnifiedPreviewGridHeaderStructure() {
    if (unifiedPreviewGridApi) {
        // updateUnifiedPreviewGrid(); // REMOVE THIS LINE to break the recursive loop
        setTimeout(() => {
            if (typeof forceColumnAlignment === 'function') {
                forceColumnAlignment(); // This should be okay, to align headers after grid changes.
            }
        }, 100);
    }
}

/**
 * Legacy compatibility - sync preview grid column width
 */
function syncPreviewGridColumnWidth(columnIndex, newWidth) {
    if (!unifiedPreviewGridApi) return;
    
    // Find the target column at the given index
    const mappingColumns = $('#horizontal-mapping-container .mapping-column-cell');
    if (columnIndex < mappingColumns.length) {
        const targetColumn = mappingColumns.eq(columnIndex).data('target-column');
        if (targetColumn) {
            // Update the AG Grid column width
            const column = unifiedPreviewGridApi.getColumn(targetColumn);
            if (column) {
                unifiedPreviewGridApi.setColumnWidth(column, newWidth);
            }
        }
    }
}

/**
 * Legacy compatibility - sync all preview grid column widths
 */
function syncAllPreviewGridColumnWidths() {
    if (!unifiedPreviewGridApi) return;
    
    $('#horizontal-mapping-container .mapping-column-cell').each(function(index) {
        const currentWidth = $(this).outerWidth();
        syncPreviewGridColumnWidth(index, currentWidth);
    });
}

/**
 * Utility function to escape HTML
 */
function escapeHtml(unsafe) {
    if (unsafe === null || typeof unsafe === 'undefined') return '';
    return String(unsafe)
         .replace(/&/g, "&amp;")
         .replace(/</g, "&lt;")
         .replace(/>/g, "&gt;")
         .replace(/"/g, "&quot;")
         .replace(/'/g, "&#039;");
}

/**
 * Create the standard modal body layout for regular columns
 */
function createStandardModalBody() {
    return `
        <div class="mb-3" id="llmCallIdArea" style="display: none;">
            <div class="alert alert-info d-flex align-items-center">
                <i class="fas fa-info-circle me-2"></i>
                <div class="flex-grow-1">
                    <strong>LLM Call ID:</strong> <span id="llmCallIdValue"></span>
                    <button type="button" class="btn btn-sm btn-outline-primary ms-2" id="copyCallIdBtn">
                        <i class="fas fa-copy me-1"></i>Copy
                    </button>
                </div>
            </div>
        </div>
        <div class="mb-3" id="columnDescriptionArea">
            <label class="form-label">Column Description:</label>
            <div class="p-2 border rounded description-text"></div>
        </div>
        <div class="mb-3" id="errorMessageArea" style="display: none;">
            <label class="form-label text-danger">Error:</label>
            <div class="p-2 border rounded bg-light text-danger error-text"></div>
        </div>
        <div class="mb-3" id="currentOutputArea" style="display: none;">
            <label class="form-label">Current Output:</label>
            <div class="p-2 border rounded bg-light" id="currentOutputContent" style="max-height: 200px; overflow-y: auto;">
                <span class="text-muted">No output available</span>
            </div>
        </div>
        <div class="mb-3" id="manualEditArea">
            <label for="cellEditValue" class="form-label">Overwrite Value:</label>
            <input type="text" class="form-control" id="cellEditValue">
            <small class="form-text text-muted">
                <span id="cellEditCharCount">0 Zeichen</span>
            </small>
        </div>
    `;
}

/**
 * Create the enhanced modal body layout with source data and prompt rendering
 */
function createProduktfamilieModalBody() {
    return `
        <div class="row">
            <!-- Left side: Source Data -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-database me-2"></i>Source Data
                        </h6>
                    </div>
                    <div class="card-body" id="sourceDataArea">
                        <div class="text-center text-muted">
                            <i class="fas fa-database fa-3x mb-3"></i>
                            <p>Loading source data...</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right side: Prompt/Edit Area -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h6 class="card-title mb-0" id="rightPanelTitle">
                            <i class="fas fa-edit me-2"></i>Edit Column
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- LLM Call ID Area -->
                        <div class="mb-3" id="llmCallIdArea" style="display: none;">
                            <div class="alert alert-info d-flex align-items-center">
                                <i class="fas fa-info-circle me-2"></i>
                                <div class="flex-grow-1">
                                    <strong>LLM Call ID:</strong> <span id="llmCallIdValue"></span>
                                    <button type="button" class="btn btn-sm btn-outline-primary ms-2" id="copyCallIdBtn">
                                        <i class="fas fa-copy me-1"></i>Copy
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Logic Column: Rendered Prompt -->
                        <div id="logicPromptArea" style="display: none;">
                            <div class="mb-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <label class="form-label mb-1 fw-bold">Rendered Prompt:</label>
                                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#renderedPromptCollapse" aria-expanded="false" aria-controls="renderedPromptCollapse">
                                        <i class="fas fa-chevron-down me-1"></i>
                                        <span class="toggle-text">Show</span>
                                    </button>
                                </div>
                                <div class="collapse" id="renderedPromptCollapse">
                                    <div class="border rounded p-2 mt-2" style="max-height: 300px; overflow-y: auto;">
                                        <div id="renderedPromptContent"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Current Output/Result -->
                        <div class="mb-3" id="currentOutputArea" style="display: none;">
                            <label class="form-label fw-bold">Current LLM Output:</label>
                            <div class="p-3 border rounded bg-light" id="currentOutputContent" style="max-height: 200px; overflow-y: auto; min-height: 60px; font-family: monospace;">
                                <span class="text-muted">No output available</span>
                            </div>
                        </div>
                        
                        <!-- Standard Column: Description -->
                        <div class="mb-3" id="columnDescriptionArea">
                            <label class="form-label">Column Description:</label>
                            <div class="p-2 border rounded description-text bg-light"></div>
                        </div>
                        
                        <div class="mb-3" id="errorMessageArea" style="display: none;">
                            <label class="form-label text-danger">Error:</label>
                            <div class="p-2 border rounded bg-light text-danger error-text"></div>
                        </div>
                        
                        <div id="manualEditArea">
                            <label for="cellEditValue" class="form-label fw-bold">Edit/Override Value:</label>
                            <input type="text" class="form-control" id="cellEditValue" placeholder="Enter value to override current output...">
                            <small class="form-text text-muted">
                                <span id="cellEditCharCount">0 Zeichen</span>
                            </small>
                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    The value above will override the current LLM output. The source data shows the original data from the import file to help you understand the context.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * Populate the source data area and handle logic prompt rendering
 */
function populateSourceDataAndPrompt(rowIndex, column, columnType) {
    try {
        // Get the source data directly from originalData
        if (!originalData || rowIndex >= originalData.length) {
            $('#sourceDataArea').html(`
                <div class="text-center text-muted">
                    <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                    <p>No source data available for this row</p>
                </div>
            `);
            return;
        }
        
        const sourceRowData = originalData[rowIndex];
        
        // Create source data display
        let sourceHtml = '<div class="source-data-content">';
        
        // Display all source fields
        let hasContent = false;
        Object.keys(sourceRowData).forEach(field => {
            const value = sourceRowData[field];
            if (value !== null && value !== undefined && value !== '') {
                const displayValue = String(value).trim();
                if (displayValue) {
                    sourceHtml += `
                        <div class="mb-2 source-field">
                            <small class="text-muted fw-bold">${escapeHtml(field)}:</small><br>
                            <span class="field-value">${escapeHtml(displayValue)}</span>
                        </div>
                    `;
                    hasContent = true;
                }
            }
        });
        
        sourceHtml += '</div>';
        
        if (!hasContent) {
            sourceHtml = `
                <div class="text-center text-muted">
                    <i class="fas fa-info-circle fa-2x mb-3"></i>
                    <p>No source data found for this row</p>
                </div>
            `;
        }
        
        // Remove inline styling as it's now handled by CSS
        // sourceHtml already contains the content
        
        $('#sourceDataArea').html(sourceHtml);
        
        // Handle logic prompt rendering
        if (columnType === 'logic') {
            const logic = columnLogic[column];
            if (logic) {
                // Update right panel title
                $('#rightPanelTitle').html(`<i class="fas fa-cogs me-2"></i>Logic Column: ${column}`);
                
                // Show logic prompt area and hide standard description
                $('#logicPromptArea').show();
                $('#columnDescriptionArea').hide();
                
                // Render the prompt with source data
                renderPromptWithSourceData(logic, sourceRowData, column);
            }
        } else {
            // Update right panel title
            $('#rightPanelTitle').html(`<i class="fas fa-edit me-2"></i>Edit ${column}`);
            
            // Hide logic prompt area and show standard description
            $('#logicPromptArea').hide();
            $('#columnDescriptionArea').show();
        }
        
    } catch (error) {
        console.error('Error populating source data:', error);
        $('#sourceDataArea').html(`
            <div class="text-center text-danger">
                <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                <p>Error loading source data</p>
                <small>${error.message}</small>
            </div>
        `);
    }
}

/**
 * Render prompt with source data substitution using existing API
 */
function renderPromptWithSourceData(logic, sourceData, column) {
    try {
        // Show loading state
        $('#renderedPromptContent').html('<span class="text-muted"><i class="fas fa-spinner fa-spin"></i> Generating preview...</span>');
        
        if (!logic) {
            $('#renderedPromptContent').html('<span class="text-muted">No logic template available</span>');
            return;
        }
        
        // Get the prompt text from the mapping
        const prompt = $(`.mapping-column-cell[data-target-column="${column}"]`).attr('data-prompt') || '';
        
        // Get user notes
        const userNotes = $('#userNotes').val() || '';
        
        // Prepare references
        const references = {
            prompt: prompt,
            notes: userNotes,
            target_column: column
        };
        
        // Make API request to get the full rendered prompt using existing endpoint
        fetch('/api/import/preview-prompt', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                logic: logic,
                rowData: [sourceData],
                references: references,
                model: enabledModels.length > 0 ? enabledModels[0].id : 'meta-llama/llama-3.1-8b-instruct:free',
                column_name: column
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Create enhanced display with both user and system prompts
                const enhancedContent = `
                    <div class="prompt-preview-container">
                        <div class="user-prompt-section">
                            <h6 class="text-primary mb-2">User Prompt:</h6>
                            <div class="prompt-content">${escapeHtml(data.user_prompt)}</div>
                        </div>
                        <div class="system-prompt-section mt-3">
                            <h6 class="text-secondary mb-2">System Prompt:</h6>
                            <div class="prompt-content system-prompt">${escapeHtml(data.system_prompt)}</div>
                        </div>
                    </div>
                `;
                $('#renderedPromptContent').html(enhancedContent);
            } else {
                $('#renderedPromptContent').html(`<span class="text-danger">Error: ${data.error || 'Unknown error'}</span>`);
            }
        })
        .catch(error => {
            console.error('Error fetching prompt preview:', error);
            $('#renderedPromptContent').html(`<span class="text-danger">Error fetching preview: ${error.message}</span>`);
        });
        
    } catch (error) {
        console.error('Error rendering prompt:', error);
        $('#renderedPromptContent').html(`<span class="text-danger">Error rendering prompt: ${error.message}</span>`);
    }
}

/**
 * Get a human-readable display name for a field
 */
function getFieldDisplayName(fieldName) {
    const displayNames = {
        'Produktfamilie': 'Product Family',
        'ARTIKELNAME': 'Article Name',
        'BESCHREIBUNG': 'Description',
        'PREIS': 'Price',
        'HERSTELLER': 'Manufacturer',
        'SKU': 'SKU',
        'TITLE': 'Title',
        'name': 'Name',
        'identifier': 'Identifier',
        'description': 'Description',
        'price': 'Price',
        'manufacturer': 'Manufacturer'
    };
    
    return displayNames[fieldName] || fieldName.replace(/_/g, ' ').replace(/([A-Z])/g, ' $1').trim();
}

/**
 * Load and display LLM call ID in the modal
 */
function loadLLMCallIdForModal(column, rowIndex) {
    try {
        // Check if we have access to job data with LLM details
        if (typeof currentJobId === 'undefined' || !currentJobId) {
            console.log('No active job to get LLM call ID from');
            return;
        }
        
        // Try to get LLM details from job data
        fetch(`/api/import/jobs/${currentJobId}`)
            .then(response => response.json())
            .then(jobData => {
                if (jobData && jobData.llm_details && 
                    jobData.llm_details[rowIndex] && 
                    jobData.llm_details[rowIndex][column]) {
                    
                    const details = jobData.llm_details[rowIndex][column];
                    const callId = details.call_id;
                    
                    if (callId) {
                        // Show the LLM call ID area
                        $('#llmCallIdArea').show();
                        $('#llmCallIdValue').text(callId);
                        
                        // Add copy functionality
                        $('#copyCallIdBtn').off('click').on('click', function() {
                            if (navigator.clipboard && navigator.clipboard.writeText) {
                                navigator.clipboard.writeText(callId).then(() => {
                                    $(this).html('<i class="fas fa-check me-1"></i>Copied!').addClass('btn-success').removeClass('btn-outline-primary');
                                    setTimeout(() => {
                                        $(this).html('<i class="fas fa-copy me-1"></i>Copy').removeClass('btn-success').addClass('btn-outline-primary');
                                    }, 2000);
                                }).catch(() => {
                                    // Fallback if clipboard API fails
                                    prompt("LLM Call ID (Ctrl+C to copy):", callId);
                                });
                            } else {
                                // Fallback for browsers without clipboard API
                                prompt("LLM Call ID (Ctrl+C to copy):", callId);
                            }
                        });
                    } else {
                        console.log('No LLM Call ID found for this cell transformation');
                    }
                } else {
                    console.log('No LLM transformation details found for this cell');
                }
            })
            .catch(error => {
                console.error('Error fetching LLM call ID:', error);
            });
            
    } catch (error) {
        console.error('Error in loadLLMCallIdForModal:', error);
    }
}

/**
 * Show modal for editing a cell or reapplying logic to a specific cell
 */
function showCellEditModal(column, rowIndex, currentValue) {
    // Find column type (one-to-one, string, or logic)
    let columnType = 'unknown';
    if (columnMappings[column]) {
        columnType = 'one-to-one';
    } else if (columnStringValues[column]) {
        columnType = 'string';
    } else if (columnLogic[column]) {
        columnType = 'logic';
    }
    
    // Get column details for the modal
    const columnDetails = mappingDefinitions.find(def => def.column === column) || {};
    const prompt = columnDetails.prompt || 'No description available';
    
    // Parse error message if applicable
    let errorMessage = '';
    if (currentValue && currentValue.startsWith('Error:')) {
        errorMessage = currentValue;
        currentValue = '';
    }
    
    // Check if we already have a modal, if not create it
    if (!$('#cellEditModal').length) {
        // Create different modal layouts based on column type
        const useEnhancedModal = columnType === 'logic' || column === 'Produktfamilie';
        const modalDialogClass = useEnhancedModal ? 'modal-dialog modal-xl' : 'modal-dialog';
        
        $('body').append(`
            <div class="modal fade" id="cellEditModal" tabindex="-1" aria-labelledby="cellEditModalLabel" aria-hidden="true">
                <div class="${modalDialogClass}">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="cellEditModalLabel">Edit Cell</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body" id="cellEditModalBody">
                            ${useEnhancedModal ? createProduktfamilieModalBody() : createStandardModalBody()}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" id="saveCellEditBtn">Save Changes</button>
                            <button type="button" class="btn btn-info" id="reapplyLogicBtn" style="display: none;">Reapply Logic</button>
                        </div>
                    </div>
                </div>
            </div>
        `);
    } else {
        // Update existing modal body based on column type
        const useEnhancedModal = columnType === 'logic' || column === 'Produktfamilie';
        const modalDialog = $('#cellEditModal .modal-dialog');
        
        if (useEnhancedModal) {
            modalDialog.removeClass('modal-dialog').addClass('modal-dialog modal-xl');
            $('#cellEditModalBody').html(createProduktfamilieModalBody());
        } else {
            modalDialog.removeClass('modal-xl').addClass('modal-dialog');
            $('#cellEditModalBody').html(createStandardModalBody());
        }
    }
    
    // Update modal content
    $('#cellEditModalLabel').text(`Edit Cell: ${column} (Row ${rowIndex + 1})`);
    $('#columnDescriptionArea .description-text').text(prompt);
    
    if (errorMessage) {
        $('#errorMessageArea').show();
        $('#errorMessageArea .error-text').text(errorMessage);
        $('#currentOutputArea').hide(); // Hide output area when there's an error
    } else {
        $('#errorMessageArea').hide();
    }
    
    // Always set the input value first, before any transformations
    $('#cellEditValue').val(currentValue);
    
    // Reset LLM call ID area initially
    $('#llmCallIdArea').hide();
    $('#llmCallIdValue').text('');
    
    // Display current output if available (but not if there's an error)
    // Force show current output area for enhanced modals
    const useEnhancedModal = columnType === 'logic' || column === 'Produktfamilie';
    if (!errorMessage) {
        if (currentValue && currentValue.trim() !== '') {
            $('#currentOutputArea').show();
            $('#currentOutputContent').html(`<span class="text-dark">${escapeHtml(currentValue)}</span>`);
        } else {
            $('#currentOutputArea').show();
            $('#currentOutputContent').html('<span class="text-muted"><em>No output generated yet</em></span>');
        }
    } else {
        // Even with errors, show the current output area for enhanced modals
        if (useEnhancedModal) {
            $('#currentOutputArea').show();
            $('#currentOutputContent').html('<span class="text-muted"><em>Error occurred - see error message above</em></span>');
        }
    }
    
    // Special handling for enhanced modal - populate source data and prompt
    if (useEnhancedModal) {
        populateSourceDataAndPrompt(rowIndex, column, columnType);
    }
    
    // Load and display LLM call ID if available
    loadLLMCallIdForModal(column, rowIndex);
    
    // Try to initialize validation dropdown for cell editing
    let dropdownInitialized = false;
    if (typeof initializeCellEditValidationDropdown === 'function') {
        initializeCellEditValidationDropdown(column, currentValue)
            .then(initialized => {
                dropdownInitialized = initialized;
                
                // Ensure current value is set after dropdown initialization
                if (initialized && currentValue) {
                    const cellEditElement = $('#cellEditValue');
                    if (cellEditElement.is('select')) {
                        cellEditElement.val(currentValue).trigger('change');
                    }
                }
                
                if (!initialized) {
                    // Update character count initially only if dropdown wasn't initialized
                    updateCellEditCharCount();
                } else {
                    // Update character count for dropdown too
                    updateCellEditCharCount();
                }
            })
            .catch(error => {
                console.error('Error initializing validation dropdown:', error);
                updateCellEditCharCount();
            });
    } else {
        // Update character count initially
        updateCellEditCharCount();
    }
    
    // Show/hide reapply logic button based on column type
    if (columnType === 'logic') {
        $('#reapplyLogicBtn').show();
    } else {
        $('#reapplyLogicBtn').hide();
    }
    
    // Remove any previous event handlers
    $('#saveCellEditBtn').off('click');
    $('#reapplyLogicBtn').off('click');
    $('#cellEditValue').off('input change');
    
    // Add event handlers
    $('#saveCellEditBtn').on('click', function() {
        const newValue = $('#cellEditValue').val();
        saveManualCellEdit(column, rowIndex, newValue);
        $('#cellEditModal').modal('hide');
    });
    
    $('#reapplyLogicBtn').on('click', function() {
        reapplyLogicToCell(column, rowIndex);
        $('#cellEditModal').modal('hide');
    });
    
    // Add character count update handler for both input and change events
    // This handles both text inputs and select dropdowns
    $('#cellEditValue').on('input change', function() {
        updateCellEditCharCount();
    });
    
    // Show the modal
    const cellEditModalInstance = new bootstrap.Modal(document.getElementById('cellEditModal'));
    cellEditModalInstance.show();

    // Ensure focus is handled correctly when modal is hidden
    $(document.getElementById('cellEditModal')).one('hidden.bs.modal', function () {
        const modalContent = this;
        if (modalContent.contains(document.activeElement)) {
            document.activeElement.blur();
        }
    });
    
    // Add event handlers for collapsible rendered prompt section
    $('#renderedPromptCollapse').on('show.bs.collapse', function () {
        const toggleBtn = $('[data-bs-target="#renderedPromptCollapse"]');
        toggleBtn.find('i').removeClass('fa-chevron-down').addClass('fa-chevron-up');
        toggleBtn.find('.toggle-text').text('Hide');
    });
    
    $('#renderedPromptCollapse').on('hide.bs.collapse', function () {
        const toggleBtn = $('[data-bs-target="#renderedPromptCollapse"]');
        toggleBtn.find('i').removeClass('fa-chevron-up').addClass('fa-chevron-down');
        toggleBtn.find('.toggle-text').text('Show');
    });
}

/**
 * Update the character count display in the cell edit modal
 */
function updateCellEditCharCount() {
    const value = $('#cellEditValue').val();
    $('#cellEditCharCount').text(value.length + ' Zeichen');
}

/**
 * Save a manually edited cell value
 */
function saveManualCellEdit(column, rowIndex, newValue) {
    // Update the value in memory
    if (!columnResults[column]) {
        columnResults[column] = [];
    }
    
    // Ensure columnResults has enough elements
    while (columnResults[column].length <= rowIndex) {
        columnResults[column].push(null);
    }
    
    columnResults[column][rowIndex] = newValue;
    
    // Update the AG Grid cell
    if (unifiedPreviewGridApi) {
        const rowNode = unifiedPreviewGridApi.getDisplayedRowAtIndex(rowIndex);
        if (rowNode) {
            const data = {...rowNode.data};
            data[column] = newValue;
            rowNode.setData(data);
        }
    }
    
    // Save to the server
    if (currentJobId) {
        // Call the API to update the cell on the server
        fetch('/api/import/update-cell', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                job_id: currentJobId,
                row_index: rowIndex,
                column_name: column,
                new_value: newValue
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', 'Cell updated successfully');
            } else {
                showAlert('error', data.message || 'Error updating cell');
                // Revert the change in AG Grid
                if (unifiedPreviewGridApi) {
                    const rowNode = unifiedPreviewGridApi.getDisplayedRowAtIndex(rowIndex);
                    if (rowNode) {
                        const data = {...rowNode.data};
                        data[column] = columnResults[column][rowIndex] || '';
                        rowNode.setData(data);
                    }
                }
            }
        })
        .catch(error => {
            console.error('Error updating cell:', error);
            showAlert('error', 'Error updating cell: ' + error.message);
        });
    }
}

/**
 * Reapply logic to a specific cell
 */
function reapplyLogicToCell(column, rowIndex) {
    // Get the logic from columnLogic
    const logic = columnLogic[column];
    if (!logic) {
        showAlert('error', 'No logic found for this column');
        return;
    }
    
    // Find the column element
    const columnElement = $(`.mapping-column-cell[data-target-column="${column}"]`);
    if (!columnElement.length) {
        showAlert('error', 'Column element not found');
        return;
    }
    
    // Update the AG Grid cell to show "Computing..."
    if (unifiedPreviewGridApi) {
        const rowNode = unifiedPreviewGridApi.getDisplayedRowAtIndex(rowIndex);
        if (rowNode) {
            const data = {...rowNode.data};
            data[column] = 'Computing...';
            rowNode.setData(data);
        }
    }
    
    // Get the notes and prompt
    const currentNotes = $('#userNotes').val() || userNotes;
    const prompt = columnElement.attr('data-prompt') || '';
    
    // Get the model setting for this column
                const selectedModel = columnLlmSettings[column]?.model || (enabledModels.length > 0 ? enabledModels[0].id : 'meta-llama/llama-3.1-8b-instruct:free');
    
    // Call the API to reapply logic
    const request = {
        job_id: currentJobId,
        column_name: column,
        logic: logic,
        data: [originalData[rowIndex]],
        row_index: rowIndex,
        references: {
            notes: currentNotes,
            prompt: prompt,
            target_column: column
        },
        model_name: selectedModel
    };
    
    fetch('/api/import/apply-logic', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
    })
    .then(response => response.json())
    .then(data => {
        // Added debug log to see actual response data
        console.log(`Response for single cell ${column}, row ${rowIndex}:`, data);
        
        if (data.success) {
            // Check if transformed_data exists and has values
            let transformedValue = null;
            if (data.transformed_data && data.transformed_data.length > 0) {
                transformedValue = data.transformed_data[0];
            } else if (data.transformed_values && data.transformed_values.length > 0) {
                transformedValue = data.transformed_values[0];
            } else {
                transformedValue = "Error: No result data";
            }
            
            // Update the result in memory
            if (!columnResults[column]) {
                columnResults[column] = [];
            }
            
            // Ensure columnResults has enough elements
            while (columnResults[column].length <= rowIndex) {
                columnResults[column].push(null);
            }
            
            columnResults[column][rowIndex] = transformedValue;
            
            // Update the AG Grid cell
            if (unifiedPreviewGridApi) {
                const rowNode = unifiedPreviewGridApi.getDisplayedRowAtIndex(rowIndex);
                if (rowNode) {
                    const data = {...rowNode.data};
                    data[column] = transformedValue;
                    rowNode.setData(data);
                }
            }
            
            showAlert('success', 'Logic reapplied successfully');
        } else {
            const errorMsg = 'Error: ' + (data.message || 'Unknown error');
            
            // Update AG Grid with error
            if (unifiedPreviewGridApi) {
                const rowNode = unifiedPreviewGridApi.getDisplayedRowAtIndex(rowIndex);
                if (rowNode) {
                    const data = {...rowNode.data};
                    data[column] = errorMsg;
                    rowNode.setData(data);
                }
            }
            
            showAlert('error', data.message || 'Error applying logic');
        }
    })
    .catch(error => {
        console.error('Error applying logic to cell:', error);
        const errorMsg = 'Error: ' + error.message;
        
        // Update AG Grid with error
        if (unifiedPreviewGridApi) {
            const rowNode = unifiedPreviewGridApi.getDisplayedRowAtIndex(rowIndex);
            if (rowNode) {
                const data = {...rowNode.data};
                data[column] = errorMsg;
                rowNode.setData(data);
            }
        }
        
        showAlert('error', 'Error applying logic: ' + error.message);
    });
}

/**
 * Clear all cells in a specific row
 */
function clearRowData(rowIndex) {
    // Show confirmation dialog
    const displayRowNumber = rowIndex + 1;
    
    if (!confirm(`Are you sure you want to clear all cells in row ${displayRowNumber}?`)) {
        return;
    }
    
    // Get current job ID
    if (!currentJobId) {
        showAlert('danger', 'No active job found');
        return;
    }
    
    // Show loading state
    showAlert('info', `Clearing row ${displayRowNumber}...`);
    
    // Make API request to clear the row
    fetch('/api/import/clear-row', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            job_id: currentJobId,
            row_index: rowIndex
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            
            // Update the grid display
            if (unifiedPreviewGridApi) {
                // Get current row data
                const rowNode = unifiedPreviewGridApi.getRowNode(rowIndex);
                if (rowNode) {
                    const newRowData = {...rowNode.data};
                    
                    // Clear all column values except rowNumber
                    data.cleared_columns.forEach(columnName => {
                        if (columnName !== 'rowNumber') {
                            newRowData[columnName] = '';
                        }
                    });
                    
                    // Update the row in the grid
                    rowNode.setData(newRowData);
                    
                    // Refresh the grid to reflect changes
                    unifiedPreviewGridApi.refreshCells();
                }
            }
            
            // Update columnResults in memory if it exists
            if (data.cleared_columns && typeof columnResults !== 'undefined' && columnResults) {
                data.cleared_columns.forEach(columnName => {
                    if (columnResults[columnName] && rowIndex < columnResults[columnName].length) {
                        columnResults[columnName][rowIndex] = '';
                    }
                });
            }
            
            // Clear any cell selection that might include this row
            clearCellSelection();
            
        } else {
            showAlert('danger', data.message || 'Failed to clear row');
        }
    })
    .catch(error => {
        console.error('Error clearing row:', error);
        showAlert('danger', `Error clearing row: ${error.message}`);
    });
}

/**
 * Clear all cells in a specific column
 */
function clearColumnData(columnName) {
    // Show confirmation dialog
    if (!confirm(`Are you sure you want to clear all cells in column "${columnName}"?`)) {
        return;
    }
    
    // Get current job ID
    if (!currentJobId) {
        showAlert('danger', 'No active job found');
        return;
    }
    
    // Show loading state
    showAlert('info', `Clearing column "${columnName}"...`);
    
    // Make API request to clear the column
    fetch('/api/import/clear-column', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            job_id: currentJobId,
            column_name: columnName
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            
            // Update the grid display
            if (unifiedPreviewGridApi) {
                // Get all rows and clear the specified column
                unifiedPreviewGridApi.forEachNode(function(rowNode) {
                    const newRowData = {...rowNode.data};
                    if (newRowData[columnName] !== undefined) {
                        newRowData[columnName] = '';
                        rowNode.setData(newRowData);
                    }
                });
                
                // Refresh the grid to reflect changes
                unifiedPreviewGridApi.refreshCells();
            }
            
            // Update columnResults in memory if it exists
            if (typeof columnResults !== 'undefined' && columnResults && columnResults[columnName]) {
                // Clear all values in the column results
                for (let i = 0; i < columnResults[columnName].length; i++) {
                    columnResults[columnName][i] = '';
                }
            }
            
            // Clear any cell selection that might include this column
            clearCellSelection();
            
        } else {
            showAlert('danger', data.message || 'Failed to clear column');
        }
    })
    .catch(error => {
        console.error('Error clearing column:', error);
        showAlert('danger', `Error clearing column: ${error.message}`);
    });
} 